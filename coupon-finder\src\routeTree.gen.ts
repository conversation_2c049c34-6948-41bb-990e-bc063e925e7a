/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as TestAccesstradeImport } from './routes/test-accesstrade'
import { Route as SearchResultsImport } from './routes/search-results'
import { Route as SearchDemoImport } from './routes/search-demo'
import { Route as LoginImport } from './routes/login'
import { Route as DealsImport } from './routes/deals'
import { Route as CouponsImport } from './routes/coupons'
import { Route as CompareImport } from './routes/compare'
import { Route as AdminImport } from './routes/admin'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const TestAccesstradeRoute = TestAccesstradeImport.update({
  id: '/test-accesstrade',
  path: '/test-accesstrade',
  getParentRoute: () => rootRoute,
} as any)

const SearchResultsRoute = SearchResultsImport.update({
  id: '/search-results',
  path: '/search-results',
  getParentRoute: () => rootRoute,
} as any)

const SearchDemoRoute = SearchDemoImport.update({
  id: '/search-demo',
  path: '/search-demo',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const DealsRoute = DealsImport.update({
  id: '/deals',
  path: '/deals',
  getParentRoute: () => rootRoute,
} as any)

const CouponsRoute = CouponsImport.update({
  id: '/coupons',
  path: '/coupons',
  getParentRoute: () => rootRoute,
} as any)

const CompareRoute = CompareImport.update({
  id: '/compare',
  path: '/compare',
  getParentRoute: () => rootRoute,
} as any)

const AdminRoute = AdminImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminImport
      parentRoute: typeof rootRoute
    }
    '/compare': {
      id: '/compare'
      path: '/compare'
      fullPath: '/compare'
      preLoaderRoute: typeof CompareImport
      parentRoute: typeof rootRoute
    }
    '/coupons': {
      id: '/coupons'
      path: '/coupons'
      fullPath: '/coupons'
      preLoaderRoute: typeof CouponsImport
      parentRoute: typeof rootRoute
    }
    '/deals': {
      id: '/deals'
      path: '/deals'
      fullPath: '/deals'
      preLoaderRoute: typeof DealsImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/search-demo': {
      id: '/search-demo'
      path: '/search-demo'
      fullPath: '/search-demo'
      preLoaderRoute: typeof SearchDemoImport
      parentRoute: typeof rootRoute
    }
    '/search-results': {
      id: '/search-results'
      path: '/search-results'
      fullPath: '/search-results'
      preLoaderRoute: typeof SearchResultsImport
      parentRoute: typeof rootRoute
    }
    '/test-accesstrade': {
      id: '/test-accesstrade'
      path: '/test-accesstrade'
      fullPath: '/test-accesstrade'
      preLoaderRoute: typeof TestAccesstradeImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/compare': typeof CompareRoute
  '/coupons': typeof CouponsRoute
  '/deals': typeof DealsRoute
  '/login': typeof LoginRoute
  '/search-demo': typeof SearchDemoRoute
  '/search-results': typeof SearchResultsRoute
  '/test-accesstrade': typeof TestAccesstradeRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/compare': typeof CompareRoute
  '/coupons': typeof CouponsRoute
  '/deals': typeof DealsRoute
  '/login': typeof LoginRoute
  '/search-demo': typeof SearchDemoRoute
  '/search-results': typeof SearchResultsRoute
  '/test-accesstrade': typeof TestAccesstradeRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/admin': typeof AdminRoute
  '/compare': typeof CompareRoute
  '/coupons': typeof CouponsRoute
  '/deals': typeof DealsRoute
  '/login': typeof LoginRoute
  '/search-demo': typeof SearchDemoRoute
  '/search-results': typeof SearchResultsRoute
  '/test-accesstrade': typeof TestAccesstradeRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/admin'
    | '/compare'
    | '/coupons'
    | '/deals'
    | '/login'
    | '/search-demo'
    | '/search-results'
    | '/test-accesstrade'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/admin'
    | '/compare'
    | '/coupons'
    | '/deals'
    | '/login'
    | '/search-demo'
    | '/search-results'
    | '/test-accesstrade'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/compare'
    | '/coupons'
    | '/deals'
    | '/login'
    | '/search-demo'
    | '/search-results'
    | '/test-accesstrade'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRoute: typeof AdminRoute
  CompareRoute: typeof CompareRoute
  CouponsRoute: typeof CouponsRoute
  DealsRoute: typeof DealsRoute
  LoginRoute: typeof LoginRoute
  SearchDemoRoute: typeof SearchDemoRoute
  SearchResultsRoute: typeof SearchResultsRoute
  TestAccesstradeRoute: typeof TestAccesstradeRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRoute: AdminRoute,
  CompareRoute: CompareRoute,
  CouponsRoute: CouponsRoute,
  DealsRoute: DealsRoute,
  LoginRoute: LoginRoute,
  SearchDemoRoute: SearchDemoRoute,
  SearchResultsRoute: SearchResultsRoute,
  TestAccesstradeRoute: TestAccesstradeRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/admin",
        "/compare",
        "/coupons",
        "/deals",
        "/login",
        "/search-demo",
        "/search-results",
        "/test-accesstrade"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/admin": {
      "filePath": "admin.tsx"
    },
    "/compare": {
      "filePath": "compare.tsx"
    },
    "/coupons": {
      "filePath": "coupons.tsx"
    },
    "/deals": {
      "filePath": "deals.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/search-demo": {
      "filePath": "search-demo.tsx"
    },
    "/search-results": {
      "filePath": "search-results.tsx"
    },
    "/test-accesstrade": {
      "filePath": "test-accesstrade.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
