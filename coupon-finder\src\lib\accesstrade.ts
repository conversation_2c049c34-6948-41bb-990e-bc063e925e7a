// AccessTrade API Service
export interface AccessTradeConfig {
  apiKey: string;
  baseUrl: string;
}

export interface CreateTrackingLinkRequest {
  campaign_id: string;
  urls: string[];
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_content?: string;
  sub1?: string;
  sub2?: string;
  sub3?: string;
  sub4?: string;
}

export interface TrackingLinkResponse {
  data: {
    error_link: string[];
    success_link: Array<{
      aff_link: string;
      first_link: string | null;
      short_link: string;
      url_origin: string;
    }>;
    suspend_url: string[];
  };
  success: boolean;
}

export interface Campaign {
  id: string;
  name: string;
  merchant: string;
  status: 'active' | 'inactive';
  commission_rate: number;
  cookie_duration: number;
}

export class AccessTradeService {
  private config: AccessTradeConfig;

  constructor(config: AccessTradeConfig) {
    this.config = config;
  }

  /**
   * Tạo tracking link cho sản phẩm
   */
  async createTrackingLink(request: CreateTrackingLinkRequest): Promise<TrackingLinkResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/v1/product_link/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `token ${this.config.apiKey}`,
        },
        body: JSON.stringify({
          ...request,
          url_enc: true, // Encode URL
        }),
      });

      if (!response.ok) {
        throw new Error(`AccessTrade API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating tracking link:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách campaigns
   */
  async getCampaigns(limit = 20, page = 1): Promise<{ data: Campaign[] }> {
    try {
      const response = await fetch(
        `${this.config.baseUrl}/v1/campaigns?limit=${limit}&page=${page}&approval=successful`,
        {
          headers: {
            'Authorization': `token ${this.config.apiKey}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`AccessTrade API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      throw error;
    }
  }

  /**
   * Tìm campaign phù hợp cho merchant
   */
  async findCampaignByMerchant(merchantName: string): Promise<Campaign | null> {
    try {
      const campaigns = await this.getCampaigns(100, 1);
      
      // Tìm campaign có tên merchant tương tự
      const matchedCampaign = campaigns.data.find(campaign => 
        campaign.merchant.toLowerCase().includes(merchantName.toLowerCase()) ||
        merchantName.toLowerCase().includes(campaign.merchant.toLowerCase())
      );

      return matchedCampaign || null;
    } catch (error) {
      console.error('Error finding campaign:', error);
      return null;
    }
  }

  /**
   * Tạo tracking link cho sản phẩm với auto-detect campaign
   */
  async createProductTrackingLink(
    productUrl: string, 
    merchantName: string,
    options: {
      utm_source?: string;
      utm_medium?: string;
      utm_campaign?: string;
      sub1?: string;
    } = {}
  ): Promise<string | null> {
    try {
      // Tìm campaign phù hợp
      const campaign = await this.findCampaignByMerchant(merchantName);
      
      if (!campaign) {
        console.warn(`No campaign found for merchant: ${merchantName}`);
        return null;
      }

      // Tạo tracking link
      const response = await this.createTrackingLink({
        campaign_id: campaign.id,
        urls: [productUrl],
        utm_source: options.utm_source || 'coupon_website',
        utm_medium: options.utm_medium || 'affiliate',
        utm_campaign: options.utm_campaign || 'product_promotion',
        sub1: options.sub1 || merchantName,
      });

      if (response.success && response.data.success_link.length > 0) {
        // Ưu tiên short_link nếu có, không thì dùng aff_link
        return response.data.success_link[0].short_link || response.data.success_link[0].aff_link;
      }

      return null;
    } catch (error) {
      console.error('Error creating product tracking link:', error);
      return null;
    }
  }

  /**
   * Validate tracking link
   */
  async validateTrackingLink(trackingLink: string): Promise<boolean> {
    try {
      // Kiểm tra format của tracking link
      const isValidFormat = trackingLink.includes('accesstrade') || 
                           trackingLink.includes('shorten.asia') ||
                           trackingLink.includes('tracking.dev.accesstrade.me');
      
      if (!isValidFormat) {
        return false;
      }

      // Test HEAD request để kiểm tra link có hoạt động không
      const response = await fetch(trackingLink, { 
        method: 'HEAD',
        mode: 'no-cors' // Tránh CORS issues
      });
      
      return true; // Nếu không throw error thì link OK
    } catch (error) {
      console.warn('Tracking link validation failed:', error);
      return false;
    }
  }
}

// Singleton instance
let accessTradeService: AccessTradeService | null = null;

export function getAccessTradeService(): AccessTradeService {
  if (!accessTradeService) {
    accessTradeService = new AccessTradeService({
      apiKey: 'txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV', // API key của bạn
      baseUrl: 'https://api.accesstrade.vn',
    });
  }
  return accessTradeService;
}

// Helper function để extract merchant từ URL
export function extractMerchantFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    // Map các domain phổ biến
    const merchantMap: Record<string, string> = {
      'shopee.vn': 'Shopee',
      'lazada.vn': 'Lazada',
      'tiki.vn': 'Tiki',
      'sendo.vn': 'Sendo',
      'thegioididong.com': 'Thế Giới Di Động',
      'fptshop.com.vn': 'FPT Shop',
      'cellphones.com.vn': 'CellphoneS',
    };

    for (const [domain, merchant] of Object.entries(merchantMap)) {
      if (hostname.includes(domain)) {
        return merchant;
      }
    }

    // Fallback: extract từ domain name
    return hostname.split('.')[0];
  } catch (error) {
    console.error('Error extracting merchant from URL:', error);
    return 'Unknown';
  }
}
