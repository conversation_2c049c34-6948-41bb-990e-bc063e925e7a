import { createFileRoute } from '@tanstack/react-router';
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Loader2,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  Copy,
  RefreshCw
} from 'lucide-react';
import { getAccessTradeService } from '../lib/accesstrade';

export const Route = createFileRoute('/test-accesstrade')({
  component: TestAccessTradePage,
});

function TestAccessTradePage() {
  const [testUrl, setTestUrl] = useState('https://shopee.vn/m/ma-giam-gia');
  const [campaignId, setCampaignId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [campaigns, setCampaigns] = useState<any[]>([]);
  const [loadingCampaigns, setLoadingCampaigns] = useState(false);

  const service = getAccessTradeService();

  // Test lấy danh sách campaigns
  const testGetCampaigns = async () => {
    setLoadingCampaigns(true);
    setError(null);
    
    try {
      console.log('🔍 Testing AccessTrade API - Get Campaigns...');
      const response = await service.getCampaigns(20, 1);
      console.log('✅ Campaigns response:', response);
      
      setCampaigns(response.data || []);
      
      if (response.data && response.data.length > 0) {
        // Auto-select first campaign
        setCampaignId(response.data[0].id);
      }
    } catch (err) {
      console.error('❌ Error getting campaigns:', err);
      setError(err instanceof Error ? err.message : 'Lỗi không xác định');
    } finally {
      setLoadingCampaigns(false);
    }
  };

  // Test tạo tracking link
  const testCreateTrackingLink = async () => {
    if (!testUrl.trim() || !campaignId.trim()) {
      setError('Vui lòng nhập URL và chọn Campaign ID');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🔍 Testing AccessTrade API - Create Tracking Link...');
      console.log('📝 Request data:', {
        campaign_id: campaignId,
        urls: [testUrl],
        utm_source: 'test_coupon_website',
        utm_medium: 'test_affiliate',
        utm_campaign: 'test_campaign',
        sub1: 'test_tracking'
      });

      const response = await service.createTrackingLink({
        campaign_id: campaignId,
        urls: [testUrl],
        utm_source: 'test_coupon_website',
        utm_medium: 'test_affiliate', 
        utm_campaign: 'test_campaign',
        sub1: 'test_tracking'
      });

      console.log('✅ Tracking link response:', response);
      setResult(response);

    } catch (err) {
      console.error('❌ Error creating tracking link:', err);
      setError(err instanceof Error ? err.message : 'Lỗi không xác định');
    } finally {
      setIsLoading(false);
    }
  };

  // Test auto-detect merchant và tạo link
  const testAutoCreateLink = async () => {
    if (!testUrl.trim()) {
      setError('Vui lòng nhập URL');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🔍 Testing Auto Create Product Tracking Link...');
      
      const trackingLink = await service.createProductTrackingLink(
        testUrl,
        'Shopee', // Auto-detect sẽ override
        {
          utm_source: 'test_auto_website',
          utm_medium: 'test_auto_affiliate',
          utm_campaign: 'test_auto_campaign',
          sub1: 'test_auto_tracking'
        }
      );

      console.log('✅ Auto tracking link result:', trackingLink);
      
      setResult({
        success: !!trackingLink,
        data: {
          success_link: trackingLink ? [{
            aff_link: trackingLink,
            short_link: trackingLink,
            url_origin: testUrl
          }] : [],
          error_link: trackingLink ? [] : [testUrl]
        }
      });

    } catch (err) {
      console.error('❌ Error auto creating tracking link:', err);
      setError(err instanceof Error ? err.message : 'Lỗi không xác định');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('✅ Đã copy vào clipboard!');
    } catch (err) {
      console.error('Copy failed:', err);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-4">🧪 Test AccessTrade API</h1>
        <p className="text-gray-600">
          Test tính năng tạo tracking link với API key thật: <code>txhrQdtZMWkHONYy2r4hzpbJSRHtWrUV</code>
        </p>
      </div>

      {/* Test Get Campaigns */}
      <Card>
        <CardHeader>
          <CardTitle>1. Test Lấy Danh Sách Campaigns</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testGetCampaigns}
            disabled={loadingCampaigns}
            className="w-full"
          >
            {loadingCampaigns ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Đang lấy campaigns...
              </>
            ) : (
              'Lấy Danh Sách Campaigns'
            )}
          </Button>

          {campaigns.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Campaigns tìm thấy ({campaigns.length}):</h4>
              <div className="max-h-40 overflow-y-auto space-y-1">
                {campaigns.slice(0, 10).map((campaign) => (
                  <div 
                    key={campaign.id} 
                    className={`p-2 border rounded cursor-pointer hover:bg-gray-50 ${
                      campaignId === campaign.id ? 'bg-blue-50 border-blue-300' : ''
                    }`}
                    onClick={() => setCampaignId(campaign.id)}
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium">{campaign.merchant || campaign.name}</span>
                      <Badge variant="secondary">{campaign.id}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Create Tracking Link */}
      <Card>
        <CardHeader>
          <CardTitle>2. Test Tạo Tracking Link (Manual)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">URL Sản Phẩm:</label>
            <Input
              value={testUrl}
              onChange={(e) => setTestUrl(e.target.value)}
              placeholder="https://shopee.vn/product/..."
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Campaign ID:</label>
            <Input
              value={campaignId}
              onChange={(e) => setCampaignId(e.target.value)}
              placeholder="Chọn campaign từ danh sách trên hoặc nhập manual"
            />
          </div>

          <Button 
            onClick={testCreateTrackingLink}
            disabled={isLoading || !testUrl.trim() || !campaignId.trim()}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Đang tạo tracking link...
              </>
            ) : (
              'Tạo Tracking Link (Manual)'
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Test Auto Create */}
      <Card>
        <CardHeader>
          <CardTitle>3. Test Auto Create (Recommended)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testAutoCreateLink}
            disabled={isLoading || !testUrl.trim()}
            className="w-full"
            variant="default"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Đang auto tạo tracking link...
              </>
            ) : (
              'Auto Tạo Tracking Link (Detect Merchant)'
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Lỗi:</strong> {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Result Display */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Kết Quả Thành Công
                </>
              ) : (
                <>
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  Kết Quả Thất Bại
                </>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Success Links */}
            {result.data?.success_link?.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium text-green-800">✅ Tracking Links Thành Công:</h4>
                {result.data.success_link.map((link: any, index: number) => (
                  <div key={index} className="p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Affiliate Link:</span>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(link.aff_link)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(link.aff_link, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <Input value={link.aff_link} readOnly className="text-xs" />
                      
                      {link.short_link && link.short_link !== link.aff_link && (
                        <>
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Short Link:</span>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(link.short_link)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(link.short_link, '_blank')}
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <Input value={link.short_link} readOnly className="text-xs" />
                        </>
                      )}
                      
                      <div className="text-xs text-gray-600">
                        <strong>Original URL:</strong> {link.url_origin}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Error Links */}
            {result.data?.error_link?.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-red-800">❌ URLs Thất Bại:</h4>
                {result.data.error_link.map((url: string, index: number) => (
                  <div key={index} className="p-2 bg-red-50 rounded border border-red-200">
                    <span className="text-sm text-red-800">{url}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Raw Response */}
            <details className="mt-4">
              <summary className="cursor-pointer font-medium">📋 Raw API Response</summary>
              <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>📋 Hướng Dẫn Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p><strong>Bước 1:</strong> Click "Lấy Danh Sách Campaigns" để xem campaigns có sẵn</p>
          <p><strong>Bước 2:</strong> Chọn campaign từ danh sách hoặc nhập Campaign ID manual</p>
          <p><strong>Bước 3:</strong> Test tạo tracking link bằng 2 cách:</p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li><strong>Manual:</strong> Cần chọn campaign ID cụ thể</li>
            <li><strong>Auto:</strong> Tự động detect merchant và tìm campaign phù hợp</li>
          </ul>
          <p><strong>Lưu ý:</strong> Nếu gặp lỗi, kiểm tra:</p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>API key có đúng không</li>
            <li>Campaign có active không</li>
            <li>URL có hợp lệ không</li>
            <li>Merchant có campaign trên AccessTrade không</li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
