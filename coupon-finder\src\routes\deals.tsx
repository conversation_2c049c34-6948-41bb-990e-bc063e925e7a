import { createFileRoute } from '@tanstack/react-router';
import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Star,
  Clock,
  TrendingUp,
  ShoppingCart,
  ExternalLink,
  Users,
  Loader2,
  AlertCircle,
} from 'lucide-react';
import { useDealsData, useInfiniteDeals } from '@/hooks/use-deals-data';
import type { TopSellingProduct, BestDeal } from '@/hooks/use-deals-data';
import { EnhancedProductCard } from '@/components/enhanced-product-card';

// Helper function để format date
function formatDate(dateString: string | null): string {
  if (!dateString) return 'Không giới hạn'

  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  } catch {
    return 'Không xác định'
  }
}

export const Route = createFileRoute('/deals')({
  component: DealsPage,
});

function DealsPage() {
  const [activeTab, setActiveTab] = useState('Tất cả')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(20)
  const [trackingLinks, setTrackingLinks] = useState<Record<string, string>>({})
  const [enableAutoTracking, setEnableAutoTracking] = useState(true)

  // Use regular hook for "Tất cả" tab, infinite hook for specific tabs
  const regularData = useDealsData()
  const infiniteData = useInfiniteDeals()

  // Choose data source based on active tab
  const { topSelling, bestDeals, isLoading, isError, error, loadMore, hasMore, isLoadingMore } =
    activeTab === 'Tất cả' ?
      { ...regularData, loadMore: undefined, hasMore: false, isLoadingMore: false } :
      infiniteData

  // Categories for tabs
  const categories = [
    'Tất cả',
    'Bán chạy',
    'Giá sốc',
  ]

  // Filter data based on active tab
  const getFilteredData = () => {
    switch (activeTab) {
      case 'Bán chạy':
        return topSelling.data || []
      case 'Giá sốc':
        // For "Giá sốc", return all data (infinite loading handles pagination)
        return bestDeals.data || []
      default: // Tất cả
        return [
          ...(topSelling.data || []),
          ...(bestDeals.data || [])
        ]
    }
  }

  const filteredData = getFilteredData()
  const totalPages = Math.ceil(filteredData.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage

  // For specific tabs (except "Tất cả"), show all data without pagination
  const paginatedData = activeTab === 'Tất cả' ?
    filteredData.slice(startIndex, startIndex + itemsPerPage) :
    filteredData

  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    setCurrentPage(1) // Reset to first page when changing tabs
  }

  const handleLoadMore = () => {
    if (activeTab === 'Tất cả') {
      // For "Tất cả" tab, use pagination
      if (currentPage < totalPages) {
        setCurrentPage(prev => prev + 1)
      }
    } else {
      // For specific tabs, use infinite loading
      if (loadMore && hasMore && !isLoadingMore) {
        loadMore()
      }
    }
  }

  // Callback để lưu tracking links đã tạo
  const handleTrackingLinkGenerated = (productId: string, trackingLink: string) => {
    setTrackingLinks(prev => ({
      ...prev,
      [productId]: trackingLink
    }))
    console.log(`✅ Tracking link generated for ${productId}:`, trackingLink)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4 text-primary' />
            <p className='text-lg text-gray-600'>Đang tải deals hot nhất...</p>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (isError) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <div className='text-center'>
            <AlertCircle className='h-8 w-8 mx-auto mb-4 text-red-500' />
            <p className='text-lg text-gray-600 mb-4'>Có lỗi xảy ra khi tải dữ liệu</p>
            <p className='text-sm text-gray-500'>{error?.message}</p>
            <Button
              onClick={() => window.location.reload()}
              className='mt-4'
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header Section */}
      <div className='text-center mb-8'>
        <h1 className='text-4xl font-bold text-gray-900 mb-4'>
          🔥 Top Deals Hôm Nay
        </h1>
        <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
          Khám phá những deal hot nhất, sản phẩm bán chạy và ưu đãi có thời hạn
        </p>

        {/* AccessTrade Integration Info */}
        <div className='mt-6 flex flex-col sm:flex-row items-center justify-center gap-4'>
          <div className='flex items-center gap-2'>
            <Badge variant={enableAutoTracking ? 'default' : 'secondary'}>
              {enableAutoTracking ? '🔗 AccessTrade Auto ON' : '🔗 AccessTrade Auto OFF'}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setEnableAutoTracking(!enableAutoTracking)}
            >
              {enableAutoTracking ? 'Tắt' : 'Bật'} Auto Tracking
            </Button>
          </div>

          {Object.keys(trackingLinks).length > 0 && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              ✅ {Object.keys(trackingLinks).length} tracking links đã tạo
            </Badge>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('/test-accesstrade', '_blank')}
          >
            🧪 Test API
          </Button>
        </div>
      </div>

      {/* Category Tabs */}
      <div className='flex flex-wrap gap-2 justify-center mb-8'>
        {categories.map(category => (
          <Badge
            key={category}
            variant={category === activeTab ? 'default' : 'secondary'}
            className='px-4 py-2 cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors'
            onClick={() => handleTabChange(category)}
          >
            {category}
          </Badge>
        ))}
      </div>

      {/* Dynamic Content Based on Active Tab */}
      {activeTab === 'Tất cả' && (
        <>
          {/* Top Selling Section */}
          <div className='mb-8'>
            <div className='flex items-center gap-2 mb-4'>
              <TrendingUp className='h-6 w-6 text-green-500' />
              <h2 className='text-2xl font-bold text-gray-900'>
                Sản Phẩm Bán Chạy
              </h2>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
              {topSelling.data?.slice(0, 3).map((product, index) => (
                <EnhancedProductCard
                  key={product.id}
                  product={{
                    id: product.id,
                    name: product.name,
                    store: product.store,
                    rating: product.rating,
                    monthlySales: product.monthlySales,
                    image: product.image,
                    affiliateLink: product.affiliateLink,
                    startDate: product.startDate,
                    endDate: product.endDate,
                    merchant: product.merchant
                  }}
                  rank={index + 1}
                  autoGenerateTracking={enableAutoTracking}
                  onTrackingLinkGenerated={handleTrackingLinkGenerated}
                />
              )) || (
                <div className='col-span-full text-center py-8'>
                  <p className='text-gray-500'>Không có sản phẩm bán chạy</p>
                </div>
              )}
            </div>
          </div>

          {/* Best Deals Section */}
          <div className='mb-8'>
            <h2 className='text-2xl font-bold text-gray-900 mb-4'>
              Deal Tốt Nhất Hôm Nay
            </h2>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
              {bestDeals.data?.slice(0, 8).map(product => (
                <EnhancedProductCard
                  key={product.id}
                  product={{
                    id: product.id,
                    name: product.name,
                    store: product.store,
                    price: product.price,
                    originalPrice: product.originalPrice,
                    discount: product.discount,
                    rating: product.rating,
                    reviews: product.reviews,
                    image: product.image,
                    affiliateLink: product.affiliateLink,
                    expiryTime: product.expiryTime,
                    isLimitedTime: product.isLimitedTime,
                    merchant: product.merchant
                  }}
                  autoGenerateTracking={enableAutoTracking}
                  onTrackingLinkGenerated={handleTrackingLinkGenerated}
                />
              )) || (
                <div className='col-span-full text-center py-8'>
                  <p className='text-gray-500'>Không có deal tốt nhất</p>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* Filtered Content for Specific Tabs */}
      {activeTab !== 'Tất cả' && (
        <div className='mb-8'>
          <h2 className='text-2xl font-bold text-gray-900 mb-4'>
            {activeTab === 'Bán chạy' && (
              <div className='flex items-center gap-2'>
                <TrendingUp className='h-6 w-6 text-green-500' />
                Sản Phẩm Bán Chạy
              </div>
            )}
            {activeTab === 'Giá sốc' && activeTab}
          </h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
            {paginatedData.length > 0 ? (
              paginatedData.map((item: any, index: number) => {
                if (activeTab === 'Bán chạy') {
                  return (
                    <EnhancedProductCard
                      key={item.id}
                      product={{
                        id: item.id,
                        name: item.name,
                        store: item.store,
                        rating: item.rating,
                        monthlySales: item.monthlySales,
                        image: item.image,
                        affiliateLink: item.affiliateLink,
                        startDate: item.startDate,
                        endDate: item.endDate,
                        merchant: item.merchant
                      }}
                      rank={index + 1}
                      autoGenerateTracking={enableAutoTracking}
                      onTrackingLinkGenerated={handleTrackingLinkGenerated}
                    />
                  )
                } else {
                  return (
                    <EnhancedProductCard
                      key={item.id}
                      product={{
                        id: item.id,
                        name: item.name,
                        store: item.store,
                        price: item.price,
                        originalPrice: item.originalPrice,
                        discount: item.discount,
                        rating: item.rating,
                        reviews: item.reviews,
                        image: item.image,
                        affiliateLink: item.affiliateLink,
                        expiryTime: item.expiryTime,
                        isLimitedTime: item.isLimitedTime,
                        merchant: item.merchant
                      }}
                      autoGenerateTracking={enableAutoTracking}
                      onTrackingLinkGenerated={handleTrackingLinkGenerated}
                    />
                  )
                }
              })
            ) : (
              <div className='col-span-full text-center py-8'>
                <p className='text-gray-500'>Không có dữ liệu cho {activeTab}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Load More Button - Show for all tabs when there's more data */}
      {(
        (activeTab === 'Tất cả' && filteredData.length > itemsPerPage && currentPage < totalPages) ||
        (activeTab !== 'Tất cả' && hasMore)
      ) && (
        <div className='text-center mt-8'>
          <Button
            variant='outline'
            size='lg'
            onClick={handleLoadMore}
            disabled={isLoading || isLoadingMore}
          >
            {(isLoading || isLoadingMore) ? (
              <>
                <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                Đang tải...
              </>
            ) : (
              activeTab === 'Tất cả' ?
                `Xem thêm deals (${currentPage}/${totalPages})` :
                'Xem thêm deals'
            )}
          </Button>
        </div>
      )}

      {/* Pagination Info - Show for all tabs */}
      {filteredData.length > 0 && (
        <div className='text-center mt-4 text-sm text-gray-600'>
          {activeTab === 'Tất cả' ? (
            `Hiển thị ${Math.min(startIndex + 1, filteredData.length)} - ${Math.min(startIndex + itemsPerPage, filteredData.length)} trong tổng số ${filteredData.length} deals`
          ) : (
            `Hiển thị ${filteredData.length} deals${hasMore ? ' (có thể tải thêm)' : ''}`
          )}
        </div>
      )}
    </div>
  );
}








