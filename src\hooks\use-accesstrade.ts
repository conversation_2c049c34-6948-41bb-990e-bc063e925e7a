import { useState, useCallback } from 'react';
import { getAccessTradeService, extractMerchantFromUrl } from '../lib/accesstrade';

export interface UseAccessTradeOptions {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  sub1?: string;
}

export interface UseAccessTradeReturn {
  createTrackingLink: (productUrl: string, merchantName?: string, options?: UseAccessTradeOptions) => Promise<string | null>;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
}

/**
 * Hook để tạo tracking link từ AccessTrade
 */
export function useAccessTrade(): UseAccessTradeReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const createTrackingLink = useCallback(async (
    productUrl: string,
    merchantName?: string,
    options: UseAccessTradeOptions = {}
  ): Promise<string | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const service = getAccessTradeService();
      
      // Auto-detect merchant nếu không được cung cấp
      const detectedMerchant = merchantName || extractMerchantFromUrl(productUrl);
      
      const trackingLink = await service.createProductTrackingLink(
        productUrl,
        detectedMerchant,
        {
          utm_source: options.utm_source || 'coupon_website',
          utm_medium: options.utm_medium || 'affiliate',
          utm_campaign: options.utm_campaign || 'product_click',
          sub1: options.sub1 || detectedMerchant,
        }
      );

      if (!trackingLink) {
        setError(`Không thể tạo tracking link cho ${detectedMerchant}. Có thể merchant chưa có campaign trên AccessTrade.`);
        return null;
      }

      return trackingLink;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi không xác định';
      setError(`Lỗi tạo tracking link: ${errorMessage}`);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    createTrackingLink,
    isLoading,
    error,
    clearError,
  };
}

/**
 * Hook để batch tạo tracking links cho nhiều sản phẩm
 */
export function useAccessTradeBatch() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState({ current: 0, total: 0 });

  const createBatchTrackingLinks = useCallback(async (
    products: Array<{ url: string; merchant?: string; id: string }>,
    options: UseAccessTradeOptions = {}
  ): Promise<Array<{ id: string; trackingLink: string | null; error?: string }>> => {
    setIsLoading(true);
    setError(null);
    setProgress({ current: 0, total: products.length });

    const results: Array<{ id: string; trackingLink: string | null; error?: string }> = [];
    const service = getAccessTradeService();

    try {
      for (let i = 0; i < products.length; i++) {
        const product = products[i];
        setProgress({ current: i + 1, total: products.length });

        try {
          const detectedMerchant = product.merchant || extractMerchantFromUrl(product.url);
          
          const trackingLink = await service.createProductTrackingLink(
            product.url,
            detectedMerchant,
            {
              utm_source: options.utm_source || 'coupon_website',
              utm_medium: options.utm_medium || 'affiliate',
              utm_campaign: options.utm_campaign || 'batch_update',
              sub1: options.sub1 || detectedMerchant,
            }
          );

          results.push({
            id: product.id,
            trackingLink,
            error: trackingLink ? undefined : `Không tìm thấy campaign cho ${detectedMerchant}`,
          });

          // Delay để tránh rate limiting
          if (i < products.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Lỗi không xác định';
          results.push({
            id: product.id,
            trackingLink: null,
            error: errorMessage,
          });
        }
      }

      return results;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi batch processing';
      setError(errorMessage);
      return results;
    } finally {
      setIsLoading(false);
      setProgress({ current: 0, total: 0 });
    }
  }, []);

  return {
    createBatchTrackingLinks,
    isLoading,
    error,
    progress,
    clearError: () => setError(null),
  };
}

/**
 * Hook để validate tracking links
 */
export function useTrackingLinkValidator() {
  const [isValidating, setIsValidating] = useState(false);

  const validateLink = useCallback(async (trackingLink: string): Promise<boolean> => {
    setIsValidating(true);
    try {
      const service = getAccessTradeService();
      return await service.validateTrackingLink(trackingLink);
    } catch (error) {
      console.error('Link validation error:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  }, []);

  return {
    validateLink,
    isValidating,
  };
}
