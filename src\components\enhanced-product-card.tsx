import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardT<PERSON>le, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ShoppingCart,
  ExternalLink,
  Star,
  Users,
  Clock,
  Loader2,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useAccessTrade } from '@/hooks/use-accesstrade';
import { extractMerchantFromUrl } from '@/lib/accesstrade';

interface ProductCardProps {
  product: {
    id: string;
    name: string;
    store: string;
    price?: number;
    originalPrice?: number;
    discount?: number;
    rating: number;
    reviews?: number;
    monthlySales?: number;
    image: string;
    affiliateLink: string;
    startDate?: string;
    endDate?: string;
    expiryTime?: string;
    isLimitedTime?: boolean;
  };
  rank?: number;
  autoGenerateTracking?: boolean;
  onTrackingLinkGenerated?: (productId: string, trackingLink: string) => void;
}

export function EnhancedProductCard({ 
  product, 
  rank,
  autoGenerateTracking = false,
  onTrackingLinkGenerated 
}: ProductCardProps) {
  const [trackingLink, setTrackingLink] = useState<string | null>(null);
  const [trackingStatus, setTrackingStatus] = useState<'idle' | 'generating' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { createTrackingLink, isLoading, error } = useAccessTrade();

  // Auto-generate tracking link khi component mount
  useEffect(() => {
    if (autoGenerateTracking && product.affiliateLink && product.affiliateLink !== '#') {
      generateTrackingLink();
    }
  }, [autoGenerateTracking, product.affiliateLink]);

  const generateTrackingLink = async () => {
    if (!product.affiliateLink || product.affiliateLink === '#') return;

    setTrackingStatus('generating');
    setErrorMessage(null);

    try {
      const merchant = extractMerchantFromUrl(product.affiliateLink);
      const link = await createTrackingLink(product.affiliateLink, merchant, {
        utm_source: 'coupon_website',
        utm_medium: 'product_card',
        utm_campaign: 'deals_page',
        sub1: product.id,
      });

      if (link) {
        setTrackingLink(link);
        setTrackingStatus('success');
        onTrackingLinkGenerated?.(product.id, link);
      } else {
        setTrackingStatus('error');
        setErrorMessage(`Không thể tạo tracking link cho ${merchant}`);
      }
    } catch (err) {
      setTrackingStatus('error');
      setErrorMessage(err instanceof Error ? err.message : 'Lỗi không xác định');
    }
  };

  const handleBuyNow = () => {
    const linkToUse = trackingLink || product.affiliateLink;
    if (linkToUse && linkToUse !== '#') {
      window.open(linkToUse, '_blank', 'noopener,noreferrer');
    }
  };

  const handleViewProduct = () => {
    const linkToUse = trackingLink || product.affiliateLink;
    if (linkToUse && linkToUse !== '#') {
      window.open(linkToUse, '_blank', 'noopener,noreferrer');
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Không xác định';
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch {
      return 'Không xác định';
    }
  };

  const displayDiscount = () => {
    if (!product.discount || product.discount < 0 || product.discount > 100) {
      return 5; // Fallback discount
    }
    return Math.round(product.discount);
  };

  const hasValidLink = (trackingLink || product.affiliateLink) && 
                      (trackingLink || product.affiliateLink) !== '#';

  return (
    <Card className='relative hover:shadow-lg transition-shadow duration-200'>
      {/* Rank badge - nếu có */}
      {rank && (
        <div className='absolute top-2 right-2 bg-yellow-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold z-10'>
          {rank}
        </div>
      )}

      {/* Tracking Status Indicator */}
      {autoGenerateTracking && (
        <div className='absolute top-2 left-2 z-10'>
          {trackingStatus === 'generating' && (
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              Tạo link...
            </Badge>
          )}
          {trackingStatus === 'success' && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              AccessTrade
            </Badge>
          )}
          {trackingStatus === 'error' && (
            <Badge variant="secondary" className="bg-red-100 text-red-800">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Lỗi
            </Badge>
          )}
        </div>
      )}

      <CardHeader className='pb-3'>
        {/* Product Image - nếu có */}
        {product.image && (
          <div className='relative mb-3'>
            <img
              src={product.image}
              alt={product.name}
              className='w-full h-48 object-cover rounded-lg'
            />
            {product.discount && (
              <Badge variant='destructive' className='absolute top-2 right-2'>
                -{displayDiscount()}%
              </Badge>
            )}
            {product.isLimitedTime && (
              <div className='absolute top-2 left-2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-bold'>
                CÓ HẠN
              </div>
            )}
          </div>
        )}

        <CardTitle className='text-base font-semibold text-gray-900 line-clamp-2'>
          {product.name}
        </CardTitle>
        <CardDescription className='text-sm text-gray-600'>
          {product.store}
        </CardDescription>
      </CardHeader>

      <CardContent className='pt-0'>
        <div className='space-y-3'>
          {/* Price - nếu có */}
          {product.price && (
            <div className='flex items-center gap-2'>
              <span className='text-xl font-bold text-red-600'>
                {product.price.toLocaleString('vi-VN')}đ
              </span>
              {product.originalPrice && (
                <span className='text-sm text-gray-500 line-through'>
                  {product.originalPrice.toLocaleString('vi-VN')}đ
                </span>
              )}
            </div>
          )}

          {/* Campaign Duration - nếu có */}
          {product.startDate && (
            <div className='space-y-1'>
              <div className='text-sm text-gray-600'>
                <span className='font-medium'>Bắt đầu:</span> {formatDate(product.startDate)}
              </div>
              {product.endDate && (
                <div className='text-sm text-gray-600'>
                  <span className='font-medium'>Kết thúc:</span> {formatDate(product.endDate)}
                </div>
              )}
            </div>
          )}

          {/* Rating and Reviews */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-1'>
              <Star className='h-4 w-4 fill-yellow-400 text-yellow-400' />
              <span className='text-sm font-medium'>{product.rating.toFixed(1)}</span>
            </div>
            {product.reviews && (
              <span className='text-sm text-gray-500'>
                ({product.reviews} đánh giá)
              </span>
            )}
          </div>

          {/* Sales Info */}
          {product.monthlySales && (
            <div className='flex items-center gap-2 text-sm text-gray-600'>
              <Users className='h-4 w-4' />
              <span>{product.monthlySales.toLocaleString('vi-VN')} đã bán</span>
            </div>
          )}

          {/* Expiry Time */}
          {product.expiryTime && (
            <div className='flex items-center gap-2 text-sm text-orange-600'>
              <Clock className='h-4 w-4' />
              <span>Còn {product.expiryTime}</span>
            </div>
          )}

          {/* Error Message */}
          {trackingStatus === 'error' && errorMessage && (
            <Alert variant="destructive" className="py-2">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                {errorMessage}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className='flex gap-2 pt-2'>
            <Button
              className='flex-1'
              size='sm'
              onClick={handleBuyNow}
              disabled={!hasValidLink || trackingStatus === 'generating'}
            >
              {trackingStatus === 'generating' ? (
                <Loader2 className='h-4 w-4 mr-2 animate-spin' />
              ) : (
                <ShoppingCart className='h-4 w-4 mr-2' />
              )}
              Mua ngay
            </Button>
            <Button
              variant='outline'
              size='sm'
              className='px-3'
              onClick={handleViewProduct}
              disabled={!hasValidLink || trackingStatus === 'generating'}
            >
              <ExternalLink className='h-4 w-4' />
            </Button>
          </div>

          {/* Manual Generate Button - nếu không auto */}
          {!autoGenerateTracking && trackingStatus === 'idle' && (
            <Button
              variant="outline"
              size="sm"
              onClick={generateTrackingLink}
              disabled={isLoading || !product.affiliateLink || product.affiliateLink === '#'}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Tạo AccessTrade Link...
                </>
              ) : (
                'Tạo AccessTrade Link'
              )}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
