import { createFileRoute } from '@tanstack/react-router';
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { But<PERSON> } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { 
  Loader2, 
  RefreshCw, 
  Download, 
  Upload,
  ExternalLink,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react';
import { TrackingLinkManager, TrackingLinkStats } from '../../components/tracking-link-manager';
import { useAccessTradeBatch } from '../../hooks/use-accesstrade';

export const Route = createFileRoute('/admin/tracking-links')({
  component: TrackingLinksAdmin,
});

interface ProductLink {
  id: string;
  name: string;
  originalUrl: string;
  trackingLink: string | null;
  merchant: string;
  status: 'pending' | 'success' | 'error';
  error?: string;
  createdAt: string;
}

function TrackingLinksAdmin() {
  const [products, setProducts] = useState<ProductLink[]>([]);
  const [bulkUrls, setBulkUrls] = useState('');
  const [stats, setStats] = useState({
    total: 0,
    successful: 0,
    failed: 0,
  });

  const { createBatchTrackingLinks, isLoading, error, progress } = useAccessTradeBatch();

  // Mock data để demo
  React.useEffect(() => {
    const mockProducts: ProductLink[] = [
      {
        id: '1',
        name: 'iPhone 15 Pro Max',
        originalUrl: 'https://shopee.vn/iphone-15-pro-max',
        trackingLink: 'https://shorten.asia/abc123',
        merchant: 'Shopee',
        status: 'success',
        createdAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Samsung Galaxy S24',
        originalUrl: 'https://lazada.vn/samsung-galaxy-s24',
        trackingLink: null,
        merchant: 'Lazada',
        status: 'error',
        error: 'Không tìm thấy campaign cho Lazada',
        createdAt: new Date().toISOString(),
      },
    ];
    setProducts(mockProducts);
    updateStats(mockProducts);
  }, []);

  const updateStats = (productList: ProductLink[]) => {
    const total = productList.length;
    const successful = productList.filter(p => p.status === 'success').length;
    const failed = productList.filter(p => p.status === 'error').length;
    
    setStats({ total, successful, failed });
  };

  const handleBulkGenerate = async () => {
    if (!bulkUrls.trim()) return;

    const urls = bulkUrls
      .split('\n')
      .map(url => url.trim())
      .filter(url => url.length > 0);

    if (urls.length === 0) return;

    const productsToProcess = urls.map((url, index) => ({
      id: `bulk_${Date.now()}_${index}`,
      url,
    }));

    try {
      const results = await createBatchTrackingLinks(productsToProcess);
      
      const newProducts: ProductLink[] = results.map(result => ({
        id: result.id,
        name: `Product ${result.id}`,
        originalUrl: productsToProcess.find(p => p.id === result.id)?.url || '',
        trackingLink: result.trackingLink,
        merchant: 'Auto-detected',
        status: result.trackingLink ? 'success' : 'error',
        error: result.error,
        createdAt: new Date().toISOString(),
      }));

      setProducts(prev => [...newProducts, ...prev]);
      updateStats([...newProducts, ...products]);
      setBulkUrls('');
    } catch (err) {
      console.error('Bulk generation error:', err);
    }
  };

  const handleRetryFailed = async () => {
    const failedProducts = products.filter(p => p.status === 'error');
    
    if (failedProducts.length === 0) return;

    const productsToRetry = failedProducts.map(p => ({
      id: p.id,
      url: p.originalUrl,
    }));

    try {
      const results = await createBatchTrackingLinks(productsToRetry);
      
      setProducts(prev => prev.map(product => {
        const result = results.find(r => r.id === product.id);
        if (result) {
          return {
            ...product,
            trackingLink: result.trackingLink,
            status: result.trackingLink ? 'success' : 'error',
            error: result.error,
          };
        }
        return product;
      }));

      updateStats(products);
    } catch (err) {
      console.error('Retry failed products error:', err);
    }
  };

  const exportResults = () => {
    const csvContent = [
      'ID,Name,Original URL,Tracking Link,Merchant,Status,Error,Created At',
      ...products.map(p => 
        `${p.id},"${p.name}","${p.originalUrl}","${p.trackingLink || ''}","${p.merchant}","${p.status}","${p.error || ''}","${p.createdAt}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `tracking-links-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Quản Lý AccessTrade Tracking Links</h1>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportResults}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" onClick={handleRetryFailed} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry Failed
          </Button>
        </div>
      </div>

      {/* Stats */}
      <TrackingLinkStats 
        totalLinks={stats.total}
        successfulLinks={stats.successful}
        failedLinks={stats.failed}
      />

      {/* Single Link Generator */}
      <TrackingLinkManager 
        onTrackingLinkCreated={(link) => {
          console.log('New tracking link created:', link);
        }}
      />

      {/* Bulk Generator */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Tạo Tracking Links Hàng Loạt
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              URLs Sản Phẩm (mỗi URL một dòng)
            </label>
            <textarea
              className="w-full h-32 p-3 border rounded-md resize-none"
              placeholder={`https://shopee.vn/product1
https://lazada.vn/product2
https://tiki.vn/product3`}
              value={bulkUrls}
              onChange={(e) => setBulkUrls(e.target.value)}
              disabled={isLoading}
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isLoading && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Đang xử lý {progress.current}/{progress.total} sản phẩm...
              </AlertDescription>
            </Alert>
          )}

          <Button 
            onClick={handleBulkGenerate}
            disabled={!bulkUrls.trim() || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Đang tạo tracking links... ({progress.current}/{progress.total})
              </>
            ) : (
              'Tạo Tracking Links Hàng Loạt'
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card>
        <CardHeader>
          <CardTitle>Kết Quả Tracking Links</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Tên Sản Phẩm</th>
                  <th className="text-left p-2">Merchant</th>
                  <th className="text-left p-2">Trạng Thái</th>
                  <th className="text-left p-2">Tracking Link</th>
                  <th className="text-left p-2">Hành Động</th>
                </tr>
              </thead>
              <tbody>
                {products.map((product) => (
                  <tr key={product.id} className="border-b hover:bg-gray-50">
                    <td className="p-2">
                      <div>
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {product.originalUrl}
                        </div>
                      </div>
                    </td>
                    <td className="p-2">
                      <Badge variant="secondary">{product.merchant}</Badge>
                    </td>
                    <td className="p-2">
                      {product.status === 'success' && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Thành công
                        </Badge>
                      )}
                      {product.status === 'error' && (
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          Lỗi
                        </Badge>
                      )}
                      {product.status === 'pending' && (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Đang xử lý
                        </Badge>
                      )}
                    </td>
                    <td className="p-2">
                      {product.trackingLink ? (
                        <div className="flex items-center gap-2">
                          <Input
                            value={product.trackingLink}
                            readOnly
                            className="text-xs"
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(product.trackingLink!, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <span className="text-gray-500 text-sm">
                          {product.error || 'Chưa có tracking link'}
                        </span>
                      )}
                    </td>
                    <td className="p-2">
                      <Button variant="outline" size="sm">
                        Chi tiết
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
