import React, { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import { Loader2, ExternalLink, Copy, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { useAccessTrade, useTrackingLinkValidator } from '../hooks/use-accesstrade';
import { extractMerchantFromUrl } from '../lib/accesstrade';

interface TrackingLinkManagerProps {
  initialUrl?: string;
  onTrackingLinkCreated?: (trackingLink: string) => void;
  className?: string;
}

export function TrackingLinkManager({ 
  initialUrl = '', 
  onTrackingLinkCreated,
  className 
}: TrackingLinkManagerProps) {
  const [productUrl, setProductUrl] = useState(initialUrl);
  const [trackingLink, setTrackingLink] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  const { createTrackingLink, isLoading, error, clearError } = useAccessTrade();
  const { validateLink, isValidating } = useTrackingLinkValidator();

  const handleCreateTrackingLink = async () => {
    if (!productUrl.trim()) return;

    clearError();
    const link = await createTrackingLink(productUrl);
    
    if (link) {
      setTrackingLink(link);
      onTrackingLinkCreated?.(link);
    }
  };

  const handleCopyLink = async () => {
    if (!trackingLink) return;
    
    try {
      await navigator.clipboard.writeText(trackingLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleTestLink = () => {
    if (trackingLink) {
      window.open(trackingLink, '_blank', 'noopener,noreferrer');
    }
  };

  const handleValidateLink = async () => {
    if (!trackingLink) return;
    
    const isValid = await validateLink(trackingLink);
    if (isValid) {
      alert('✅ Tracking link hoạt động bình thường!');
    } else {
      alert('❌ Tracking link có vấn đề hoặc không thể truy cập!');
    }
  };

  const detectedMerchant = productUrl ? extractMerchantFromUrl(productUrl) : '';

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ExternalLink className="h-5 w-5" />
          Tạo AccessTrade Tracking Link
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Input URL */}
        <div className="space-y-2">
          <label className="text-sm font-medium">URL Sản Phẩm Gốc</label>
          <Input
            placeholder="https://shopee.vn/product/..."
            value={productUrl}
            onChange={(e) => setProductUrl(e.target.value)}
            disabled={isLoading}
          />
          {detectedMerchant && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Merchant được phát hiện:</span>
              <Badge variant="secondary">{detectedMerchant}</Badge>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Create Button */}
        <Button 
          onClick={handleCreateTrackingLink}
          disabled={!productUrl.trim() || isLoading}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Đang tạo tracking link...
            </>
          ) : (
            'Tạo Tracking Link'
          )}
        </Button>

        {/* Tracking Link Result */}
        {trackingLink && (
          <div className="space-y-3 p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-800">Tracking Link đã tạo thành công!</span>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-green-800">Tracking Link:</label>
              <div className="flex gap-2">
                <Input
                  value={trackingLink}
                  readOnly
                  className="bg-white"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyLink}
                  className="shrink-0"
                >
                  {copied ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleTestLink}
                className="flex-1"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Test Link
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleValidateLink}
                disabled={isValidating}
                className="flex-1"
              >
                {isValidating ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Validate
              </Button>
            </div>
          </div>
        )}

        {/* Info */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>💡 <strong>Lưu ý:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Tracking link sẽ tự động phát hiện merchant từ URL</li>
            <li>Nếu merchant chưa có campaign trên AccessTrade, sẽ không tạo được link</li>
            <li>Link tracking có thời gian sống và cookie duration theo campaign</li>
            <li>Sử dụng link rút gọn để tối ưu SEO và user experience</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

// Component để hiển thị thống kê tracking links
export function TrackingLinkStats({ 
  totalLinks, 
  successfulLinks, 
  failedLinks 
}: { 
  totalLinks: number;
  successfulLinks: number;
  failedLinks: number;
}) {
  const successRate = totalLinks > 0 ? (successfulLinks / totalLinks * 100).toFixed(1) : '0';

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold">{totalLinks}</div>
          <div className="text-sm text-gray-600">Tổng Links</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-green-600">{successfulLinks}</div>
          <div className="text-sm text-gray-600">Thành Công</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-red-600">{failedLinks}</div>
          <div className="text-sm text-gray-600">Thất Bại</div>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent className="p-4">
          <div className="text-2xl font-bold text-blue-600">{successRate}%</div>
          <div className="text-sm text-gray-600">Tỷ Lệ Thành Công</div>
        </CardContent>
      </Card>
    </div>
  );
}
